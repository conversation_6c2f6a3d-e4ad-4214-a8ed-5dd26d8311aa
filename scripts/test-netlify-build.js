#!/usr/bin/env node

/**
 * Test script to verify Netlify build configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Netlify build configuration...\n');

// Check if required files exist
const requiredFiles = [
  'netlify.toml',
  '.nvmrc',
  'public/_redirects',
  'package.json'
];

console.log('📁 Checking required files:');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
  }
});

// Check Node.js version
console.log('\n🔧 Node.js version:');
try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  console.log(`Current: ${nodeVersion}`);
  
  const nvmrcVersion = fs.readFileSync('.nvmrc', 'utf8').trim();
  console.log(`Expected (.nvmrc): v${nvmrcVersion}`);
} catch (error) {
  console.log('❌ Error checking Node.js version:', error.message);
}

// Test build command
console.log('\n🏗️  Testing build command:');
try {
  console.log('Running: npm install --legacy-peer-deps');
  execSync('npm install --legacy-peer-deps', { stdio: 'inherit' });
  
  console.log('Running: npm run build');
  execSync('npm run build', { stdio: 'inherit' });
  
  console.log('✅ Build successful!');
} catch (error) {
  console.log('❌ Build failed:', error.message);
}

// Check if .next directory exists
console.log('\n📦 Checking build output:');
if (fs.existsSync('.next')) {
  console.log('✅ .next directory exists');
  
  // Check for key files
  const keyFiles = [
    '.next/BUILD_ID',
    '.next/server',
    '.next/static'
  ];
  
  keyFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} exists`);
    } else {
      console.log(`❌ ${file} missing`);
    }
  });
} else {
  console.log('❌ .next directory missing');
}

console.log('\n🎉 Netlify configuration test complete!');
console.log('\n📝 Next steps:');
console.log('1. Commit your changes to git');
console.log('2. Push to your repository');
console.log('3. Connect your repository to Netlify');
console.log('4. Netlify should automatically detect the configuration');
