{"name": "decapcms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test-config": "node scripts/test-config.js", "test-netlify": "node scripts/test-netlify-build.js"}, "dependencies": {"next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@netlify/plugin-nextjs": "^5.11.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}