function computeReadTime(text) {
  const wordsPerMinute = 150; // slower pace for more accurate read time
  const words = (text || '').trim().split(/\s+/).filter(Boolean).length;
  const minutes = Math.max(1, <PERSON>.ceil(words / wordsPerMinute));
  return minutes + ' min read';
}

CMS.registerEventListener({
  name: 'preSave',
  handler: ({ entry }) => {
    const body = entry.getIn(['data', 'body']) || '';
    const plain = body.replace(/<[^>]+>/g, '');
    const readTime = computeReadTime(plain);
    return entry.setIn(['data', 'readTime'], readTime);
  }
});
