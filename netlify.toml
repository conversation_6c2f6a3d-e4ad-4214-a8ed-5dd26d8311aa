[build]
  # Build command with legacy peer deps to handle dependency conflicts
  command = "npm install --legacy-peer-deps && npm run build"
  publish = ".next"

  # Functions directory for API routes
  functions = ".netlify/functions"

[build.environment]
  # Node.js version
  NODE_VERSION = "20"

  # NPM version
  NPM_VERSION = "10"

# Redirects for DecapCMS
[[redirects]]
  from = "/admin"
  to = "/admin/index.html"
  status = 200

[[redirects]]
  from = "/config.yml"
  to = "/admin/config.yml"
  status = 200

# Headers for admin files
[[headers]]
  for = "/admin/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    Cache-Control = "no-cache, no-store, must-revalidate"

[[headers]]
  for = "/admin/config.yml"
  [headers.values]
    Content-Type = "text/yaml; charset=utf-8"
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "/config.yml"
  [headers.values]
    Content-Type = "text/yaml; charset=utf-8"
    Cache-Control = "public, max-age=0, must-revalidate"

# Netlify plugins
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Next.js specific settings
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true
